/* ===== PREMIUM MEDICAL EDUCATION INTERACTIONS ===== */
/* Advanced JavaScript for smooth, professional user experience */

class PremiumInteractions {
  constructor() {
    this.init();
  }

  init() {
    this.setupScrollReveal();
    this.setupSmoothScrolling();
    this.setupParallaxEffects();
    this.setupMagneticElements();
    this.setupAdvancedLoading();
    this.setupPremiumForms();
    this.setupPerformanceOptimization();
  }

  // ===== SCROLL REVEAL ANIMATIONS =====
  setupScrollReveal() {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('revealed');
          
          // Stagger animations for child elements
          const staggerItems = entry.target.querySelectorAll('.stagger-item');
          staggerItems.forEach((item, index) => {
            setTimeout(() => {
              item.classList.add('animate');
            }, index * 100);
          });
        }
      });
    }, observerOptions);

    // Observe elements with reveal classes
    document.querySelectorAll('.reveal-up, .reveal-left, .reveal-right, .reveal-scale').forEach(el => {
      observer.observe(el);
    });
  }

  // ===== SMOOTH SCROLLING WITH EASING =====
  setupSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', (e) => {
        e.preventDefault();
        const target = document.querySelector(anchor.getAttribute('href'));
        
        if (target) {
          const headerOffset = 80;
          const elementPosition = target.getBoundingClientRect().top;
          const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

          this.smoothScrollTo(offsetPosition, 1000);
        }
      });
    });
  }

  smoothScrollTo(targetPosition, duration) {
    const startPosition = window.pageYOffset;
    const distance = targetPosition - startPosition;
    let startTime = null;

    const easeInOutCubic = (t) => {
      return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
    };

    const animation = (currentTime) => {
      if (startTime === null) startTime = currentTime;
      const timeElapsed = currentTime - startTime;
      const progress = Math.min(timeElapsed / duration, 1);
      const ease = easeInOutCubic(progress);
      
      window.scrollTo(0, startPosition + distance * ease);
      
      if (timeElapsed < duration) {
        requestAnimationFrame(animation);
      }
    };

    requestAnimationFrame(animation);
  }

  // ===== PARALLAX EFFECTS =====
  setupParallaxEffects() {
    const parallaxElements = document.querySelectorAll('.parallax-element');
    
    if (parallaxElements.length === 0) return;

    let ticking = false;

    const updateParallax = () => {
      const scrolled = window.pageYOffset;
      
      parallaxElements.forEach(element => {
        const rate = scrolled * -0.5;
        element.style.transform = `translateY(${rate}px)`;
      });
      
      ticking = false;
    };

    window.addEventListener('scroll', () => {
      if (!ticking) {
        requestAnimationFrame(updateParallax);
        ticking = true;
      }
    });
  }

  // ===== MAGNETIC HOVER EFFECTS =====
  setupMagneticElements() {
    const magneticElements = document.querySelectorAll('.magnetic-hover');
    
    magneticElements.forEach(element => {
      element.addEventListener('mousemove', (e) => {
        const rect = element.getBoundingClientRect();
        const x = e.clientX - rect.left - rect.width / 2;
        const y = e.clientY - rect.top - rect.height / 2;
        
        const moveX = x * 0.1;
        const moveY = y * 0.1;
        
        element.style.transform = `translate(${moveX}px, ${moveY}px)`;
      });
      
      element.addEventListener('mouseleave', () => {
        element.style.transform = 'translate(0, 0)';
      });
    });
  }

  // ===== ADVANCED LOADING SCREEN =====
  setupAdvancedLoading() {
    const loader = document.createElement('div');
    loader.className = 'premium-loader';
    loader.innerHTML = `
      <div class="loading-content">
        <div class="medical-pulse"></div>
        <div class="loading-text">
          <h3>Preparing Your Medical Journey</h3>
          <div class="progress-bar">
            <div class="progress-fill"></div>
          </div>
          <p class="loading-percentage">0%</p>
        </div>
      </div>
    `;
    
    document.body.appendChild(loader);
    
    // Simulate loading progress
    let progress = 0;
    const progressFill = loader.querySelector('.progress-fill');
    const progressText = loader.querySelector('.loading-percentage');
    
    const updateProgress = () => {
      progress += Math.random() * 15;
      if (progress > 100) progress = 100;
      
      progressFill.style.width = `${progress}%`;
      progressText.textContent = `${Math.round(progress)}%`;
      
      if (progress < 100) {
        setTimeout(updateProgress, 100 + Math.random() * 200);
      } else {
        setTimeout(() => {
          loader.style.opacity = '0';
          setTimeout(() => {
            loader.remove();
            document.body.classList.add('loaded');
          }, 500);
        }, 500);
      }
    };
    
    // Start loading after a short delay
    setTimeout(updateProgress, 300);
  }

  // ===== PREMIUM FORM INTERACTIONS =====
  setupPremiumForms() {
    const formInputs = document.querySelectorAll('input, textarea, select');
    
    formInputs.forEach(input => {
      // Floating label effect
      const wrapper = document.createElement('div');
      wrapper.className = 'premium-input-wrapper';
      input.parentNode.insertBefore(wrapper, input);
      wrapper.appendChild(input);
      
      // Add focus effects
      input.addEventListener('focus', () => {
        wrapper.classList.add('focused');
      });
      
      input.addEventListener('blur', () => {
        if (!input.value) {
          wrapper.classList.remove('focused');
        }
      });
      
      // Real-time validation feedback
      input.addEventListener('input', () => {
        this.validateInput(input);
      });
    });
  }

  validateInput(input) {
    const wrapper = input.closest('.premium-input-wrapper');
    
    // Remove existing validation classes
    wrapper.classList.remove('valid', 'invalid');
    
    // Basic validation
    if (input.type === 'email') {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (emailRegex.test(input.value)) {
        wrapper.classList.add('valid');
      } else if (input.value) {
        wrapper.classList.add('invalid');
      }
    } else if (input.required && input.value) {
      wrapper.classList.add('valid');
    } else if (input.required && !input.value) {
      wrapper.classList.add('invalid');
    }
  }

  // ===== PERFORMANCE OPTIMIZATION =====
  setupPerformanceOptimization() {
    // Debounce scroll events
    let scrollTimeout;
    window.addEventListener('scroll', () => {
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }
      scrollTimeout = setTimeout(() => {
        this.handleScroll();
      }, 16); // ~60fps
    });

    // Lazy load images
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          img.src = img.dataset.src;
          img.classList.add('loaded');
          imageObserver.unobserve(img);
        }
      });
    });

    images.forEach(img => imageObserver.observe(img));
  }

  handleScroll() {
    const scrolled = window.pageYOffset;
    const rate = scrolled * -0.5;
    
    // Update header transparency
    const header = document.querySelector('.header');
    if (header) {
      if (scrolled > 100) {
        header.classList.add('scrolled');
      } else {
        header.classList.remove('scrolled');
      }
    }
  }

  // ===== UTILITY METHODS =====
  createParticles(container, count = 20) {
    for (let i = 0; i < count; i++) {
      const particle = document.createElement('div');
      particle.className = 'particle';
      particle.style.left = Math.random() * 100 + '%';
      particle.style.animationDelay = Math.random() * 6 + 's';
      particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
      container.appendChild(particle);
    }
  }

  addRippleEffect(element) {
    element.addEventListener('click', (e) => {
      const ripple = document.createElement('span');
      const rect = element.getBoundingClientRect();
      const size = Math.max(rect.width, rect.height);
      const x = e.clientX - rect.left - size / 2;
      const y = e.clientY - rect.top - size / 2;
      
      ripple.style.width = ripple.style.height = size + 'px';
      ripple.style.left = x + 'px';
      ripple.style.top = y + 'px';
      ripple.classList.add('ripple');
      
      element.appendChild(ripple);
      
      setTimeout(() => {
        ripple.remove();
      }, 600);
    });
  }
}

// Initialize premium interactions when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new PremiumInteractions();
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = PremiumInteractions;
}
