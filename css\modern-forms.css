/* ===== MODERN FORM STYLES ===== */
/* Professional, Accessible Form Design */

/* ===== FORM CONTAINER ===== */
.application-form {
  max-width: var(--container-2xl);
  margin: var(--space-16) auto;
  background: var(--color-surface);
  border-radius: var(--radius-3xl);
  padding: var(--space-12);
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--color-border);
  position: relative;
  overflow: hidden;
}

.application-form::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-500), var(--secondary-500));
}

.application-form h3 {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--color-text-primary);
  text-align: center;
  margin-bottom: var(--space-2);
}

.application-form > p {
  text-align: center;
  color: var(--color-text-secondary);
  font-size: var(--text-lg);
  margin-bottom: var(--space-10);
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

/* ===== FORM LAYOUT ===== */
#enquiry-form {
  display: grid;
  gap: var(--space-6);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-6);
}

.form-group {
  position: relative;
  display: flex;
  flex-direction: column;
}

/* ===== FORM LABELS ===== */
.form-group label {
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-2);
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.form-group label::after {
  content: '*';
  color: var(--color-error);
  font-weight: var(--font-bold);
  margin-left: var(--space-1);
}

.form-group label[for="qualification"]::after,
.form-group label[for="message"]::after {
  display: none;
}

/* ===== FORM INPUTS ===== */
.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: var(--space-4) var(--space-4);
  border: 2px solid var(--color-border);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  font-family: var(--font-sans);
  background: var(--color-surface);
  color: var(--color-text-primary);
  transition: var(--transition-default);
  min-height: 48px; /* Touch target */
  appearance: none;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: var(--shadow-focus-primary);
  background: var(--primary-50);
}

.form-group input:hover,
.form-group select:hover,
.form-group textarea:hover {
  border-color: var(--primary-300);
}

/* ===== INPUT STATES ===== */
.form-group input:valid,
.form-group select:valid,
.form-group textarea:valid {
  border-color: var(--color-success);
}

.form-group input:invalid:not(:placeholder-shown),
.form-group select:invalid:not(:placeholder-shown),
.form-group textarea:invalid:not(:placeholder-shown) {
  border-color: var(--color-error);
  background: var(--error-50);
}

.form-group input:invalid:not(:placeholder-shown):focus,
.form-group select:invalid:not(:placeholder-shown):focus,
.form-group textarea:invalid:not(:placeholder-shown):focus {
  box-shadow: var(--shadow-focus-error);
}

/* ===== PLACEHOLDER STYLES ===== */
.form-group input::placeholder,
.form-group textarea::placeholder {
  color: var(--color-text-muted);
  font-style: italic;
}

/* ===== SELECT DROPDOWN ===== */
.form-group select {
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%236b7280" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6,9 12,15 18,9"></polyline></svg>');
  background-repeat: no-repeat;
  background-position: right var(--space-4) center;
  background-size: 16px;
  padding-right: var(--space-12);
  cursor: pointer;
}

.form-group select:focus {
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%232563eb" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6,9 12,15 18,9"></polyline></svg>');
}

/* ===== TEXTAREA ===== */
.form-group textarea {
  resize: vertical;
  min-height: 120px;
  line-height: var(--leading-relaxed);
}

/* ===== ERROR MESSAGES ===== */
.error-message {
  font-size: var(--text-sm);
  color: var(--color-error);
  margin-top: var(--space-2);
  display: flex;
  align-items: center;
  gap: var(--space-1);
  opacity: 0;
  transform: translateY(-4px);
  transition: var(--transition-default);
}

.error-message::before {
  content: '⚠';
  font-size: var(--text-base);
}

.form-group.error .error-message {
  opacity: 1;
  transform: translateY(0);
}

/* ===== SUCCESS STATES ===== */
.form-group.success input,
.form-group.success select,
.form-group.success textarea {
  border-color: var(--color-success);
  background: var(--success-50);
}

.form-group.success input:focus,
.form-group.success select:focus,
.form-group.success textarea:focus {
  box-shadow: var(--shadow-focus-success);
}

/* ===== SUBMIT BUTTON ===== */
.submit-btn {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  color: white;
  border: none;
  padding: var(--space-4) var(--space-8);
  border-radius: var(--radius-full);
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  cursor: pointer;
  transition: var(--transition-default);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  min-height: 56px;
  margin-top: var(--space-8);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.submit-btn:hover {
  background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.submit-btn:focus {
  outline: none;
  box-shadow: var(--shadow-focus-primary), var(--shadow-xl);
}

.submit-btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow-lg);
}

.submit-btn:disabled {
  background: var(--gray-400);
  cursor: not-allowed;
  transform: none;
  box-shadow: var(--shadow-sm);
}

.submit-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-slow);
}

.submit-btn:hover::before {
  left: 100%;
}

/* ===== LOADING STATE ===== */
.submit-btn.loading {
  pointer-events: none;
}

.submit-btn.loading span {
  opacity: 0;
}

.submit-btn.loading::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== FORM VALIDATION ICONS ===== */
.form-group {
  position: relative;
}

.form-group.success::after {
  content: '✓';
  position: absolute;
  right: var(--space-4);
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-success);
  font-weight: var(--font-bold);
  font-size: var(--text-lg);
  pointer-events: none;
}

.form-group.error::after {
  content: '✕';
  position: absolute;
  right: var(--space-4);
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-error);
  font-weight: var(--font-bold);
  font-size: var(--text-lg);
  pointer-events: none;
}

/* Adjust for textarea */
.form-group:has(textarea).success::after,
.form-group:has(textarea).error::after {
  top: var(--space-12);
  transform: none;
}

/* ===== FORM PROGRESS INDICATOR ===== */
.form-progress {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--space-8);
  padding: 0 var(--space-4);
}

.progress-step {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
  color: var(--color-text-muted);
}

.progress-step.active {
  color: var(--color-primary);
  font-weight: var(--font-semibold);
}

.progress-step.completed {
  color: var(--color-success);
}

.progress-step::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--color-border);
  transition: var(--transition-default);
}

.progress-step.active::before {
  background: var(--color-primary);
}

.progress-step.completed::before {
  background: var(--color-success);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .application-form {
    margin: var(--space-8) var(--space-4);
    padding: var(--space-8);
    border-radius: var(--radius-2xl);
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  .form-group input,
  .form-group select,
  .form-group textarea {
    padding: var(--space-3) var(--space-4);
    font-size: var(--text-base);
  }
  
  .submit-btn {
    padding: var(--space-4) var(--space-6);
    font-size: var(--text-base);
  }
  
  .form-progress {
    flex-direction: column;
    gap: var(--space-2);
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .application-form {
    margin: var(--space-6) var(--space-2);
    padding: var(--space-6);
  }
  
  .application-form h3 {
    font-size: var(--text-2xl);
  }
  
  .submit-btn {
    width: 100%;
  }
}
