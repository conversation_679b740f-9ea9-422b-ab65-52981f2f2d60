<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>MBBS Vostrix - Study Medicine at FEFU, Russia | WHO & NMC Recognized</title>
  
  <!-- SEO Meta Tags -->
  <meta name="description" content="Study MBBS at Far Eastern Federal University (FEFU), Russia. WHO & NMC recognized, English medium, affordable fees starting from ₹4.2L/year. Apply now for 2025!">
  <meta name="keywords" content="MBBS in Russia, FEFU, medical education, WHO recognized, NMC approved, study abroad, medical university">
  <meta name="author" content="MBBS Vostrix">
  
  <!-- Open Graph -->
  <meta property="og:title" content="MBBS Vostrix - Study Medicine at FEFU, Russia">
  <meta property="og:description" content="English-medium MBBS program at Far Eastern Federal University. WHO & NMC recognized. Affordable fees starting from ₹4.2L/year for 2025.">
  <meta property="og:image" content="https://mbbsvostrix.com/assets/og-image.svg">

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:url" content="https://mbbsvostrix.com/">
  <meta property="twitter:title" content="MBBS Vostrix - Study Medicine at FEFU, Russia">
  <meta property="twitter:description" content="English-medium MBBS program at Far Eastern Federal University. WHO & NMC recognized. Affordable fees starting from ₹4.2L/year for 2025.">
  <meta property="twitter:image" content="https://mbbsvostrix.com/assets/twitter-image.svg">

  <!-- Modern Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
  
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  
  <!-- PWA Icons -->
  <link rel="icon" type="image/svg+xml" href="assets/favicon.svg">
  <link rel="apple-touch-icon" sizes="180x180" href="assets/icons/icon-192.svg">
  <link rel="icon" type="image/svg+xml" sizes="32x32" href="assets/icons/icon-32.svg">
  <link rel="icon" type="image/svg+xml" sizes="16x16" href="assets/icons/icon-16.svg">

  <!-- PWA Manifest (disabled for file:// URLs to avoid CORS warning) -->
  <!-- <link rel="manifest" href="manifest.json"> -->
  <meta name="theme-color" content="#0056b3">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">
  <meta name="apple-mobile-web-app-title" content="MBBS Vostrix">

  <!-- Modern Design System -->
  <link rel="stylesheet" href="css/design-system.css">
  <link rel="stylesheet" href="css/modern-styles.css">
  <link rel="stylesheet" href="css/modern-components.css">
  <link rel="stylesheet" href="css/modern-forms.css">
  <link rel="stylesheet" href="css/modern-footer.css">
  
  <!-- GSAP for animations -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
  
  <style>
    /* Loading spinner styles */
    .component-loading {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 200px;
      background: #f8f9fa;
      border-radius: 8px;
    }
    
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #0056b3;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    /* Debug button */
    .debug-btn {
      position: fixed;
      top: 10px;
      right: 10px;
      z-index: 9999;
      padding: 10px;
      background: #dc3545;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
    }
  </style>
</head>
<body>
  <!-- Debug button (shows when ?debug=true) -->
  <button id="debug-btn" class="debug-btn" style="display: none;" onclick="clearAllLoadingStates()">
    Clear Loading States
  </button>

  <header role="banner">
    <nav class="navbar" role="navigation" aria-label="Main navigation">
      <div class="nav-container">
        <div class="nav-brand">
          <h1>MBBS Vostrix</h1>
          <span class="tagline">Your Gateway to Medical Excellence</span>
        </div>
        
        <button class="nav-toggle" aria-label="Toggle navigation" aria-expanded="false">
          <span></span>
          <span></span>
          <span></span>
        </button>
        
        <ul class="nav-menu" role="menubar">
          <li role="none"><a href="#home" role="menuitem">Home</a></li>
          <li role="none"><a href="#about" role="menuitem">About FEFU</a></li>
          <li role="none"><a href="#program" role="menuitem">Fees & Program</a></li>
          <li role="none"><a href="#admission-process" role="menuitem">Admission</a></li>
          <li role="none"><a href="#campus" role="menuitem">Campus Life</a></li>
          <li role="none"><a href="#testimonials" role="menuitem">Success Stories</a></li>
          <li role="none"><a href="#apply" role="menuitem" class="cta-nav">Apply Now</a></li>
        </ul>

        <!-- Quick Info Bar -->
        <div class="quick-info-bar" id="quick-info-bar">
          <div class="quick-info-content">
            <div class="quick-info-item">
              <span class="info-label">Application Deadline:</span>
              <span class="info-value">July 31, 2025</span>
            </div>
            <div class="quick-info-item">
              <span class="info-label">Starting Fee:</span>
              <span class="info-value">₹4.2L/year</span>
            </div>
            <div class="quick-info-item">
              <span class="info-label">Next Intake:</span>
              <span class="info-value">September 2025</span>
            </div>
            <button class="quick-info-close" onclick="document.getElementById('quick-info-bar').style.display='none'">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>
      </div>
    </nav>
  </header>

  <main id="main-content">
    <section id="home" class="hero-section" role="banner">
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="hero-title">
            Study <span class="highlight">MBBS</span> at 
            <span class="university-name">FEFU, Russia</span>
          </h1>
          <p class="hero-subtitle">
            World-class medical education in English at Far Eastern Federal University.
            WHO & NMC recognized with affordable fees starting from ₹4.2L/year.
          </p>
          <div class="hero-cta">
            <a href="#apply" class="cta-button primary">
              <span>Apply Now</span>
              <i class="fas fa-arrow-right" aria-hidden="true"></i>
            </a>
            <a href="#program" class="cta-button secondary">
              <span>Learn More</span>
              <i class="fas fa-info-circle" aria-hidden="true"></i>
            </a>
          </div>
          <div class="hero-stats">
            <div class="stat">
              <span class="stat-number">6</span>
              <span class="stat-label">Years Program</span>
            </div>
            <div class="stat">
              <span class="stat-number">₹4.2L</span>
              <span class="stat-label">Starting Fee/Year</span>
            </div>
            <div class="stat">
              <span class="stat-number">100%</span>
              <span class="stat-label">English Medium</span>
            </div>
          </div>

          <div class="urgency-indicators">
            <div class="urgency-item">
              <i class="fas fa-clock"></i>
              <span>Application deadline: <strong>July 31, 2025</strong></span>
            </div>
            <div class="urgency-item">
              <i class="fas fa-users"></i>
              <span><strong>312 students</strong> applied this month</span>
            </div>
            <div class="urgency-item">
              <i class="fas fa-chart-line"></i>
              <span><strong>Limited seats</strong> available for 2025 intake</span>
            </div>
          </div>
        </div>
        <div class="hero-visual">
          <div id="globe-container" class="globe-container" aria-label="Interactive 3D globe showing FEFU location">
            <div class="component-loading">
              <div class="loading-spinner"></div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section id="program" class="section-padding" data-component="info-cards">
      <h2 class="section-title">MBBS Program Details</h2>
      <p class="section-description">Comprehensive information about fees, accommodation, eligibility, and program structure at FEFU.</p>
      <div id="info-cards-container" class="info-cards-container">
        <div class="component-loading">
          <div class="loading-spinner"></div>
        </div>
      </div>
    </section>

    <section id="campus" class="section-padding" data-component="campus-gallery">
      <h2 class="section-title">Campus Life at FEFU</h2>
      <p class="section-description">Experience world-class education on FEFU's stunning Russky Island campus.</p>
      <div id="campus-gallery" class="campus-gallery">
        <div class="component-loading">
          <div class="loading-spinner"></div>
        </div>
      </div>
    </section>

    <section id="testimonials" class="section-padding testimonials-section">
      <h2 class="section-title">Student Success Stories</h2>
      <p class="section-description">Hear from our Indian students who are thriving at FEFU and building successful medical careers</p>

      <div class="testimonials-grid">
        <div class="testimonial-card featured">
          <div class="testimonial-header">
            <img src="https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=100&h=100&fit=crop&auto=format&q=80" alt="Priya Sharma" class="student-photo">
            <div class="student-info">
              <h3>Dr. Priya Sharma</h3>
              <p>MBBS Graduate 2023 | Now practicing in Mumbai</p>
              <div class="student-location">
                <i class="fas fa-map-marker-alt"></i>
                <span>From: Delhi | Currently: Mumbai</span>
              </div>
            </div>
          </div>
          <div class="testimonial-content">
            <div class="quote-icon">"</div>
            <p>"FEFU gave me the perfect foundation for my medical career. The English-medium education, modern facilities, and supportive faculty made my 6 years in Russia incredibly rewarding. I cleared my MCI screening exam in the first attempt and am now practicing as a resident doctor in Mumbai. The international exposure and quality education at FEFU prepared me well for the Indian medical system."</p>
            <div class="testimonial-stats">
              <div class="stat">
                <span class="stat-number">1st</span>
                <span class="stat-label">Attempt MCI Clear</span>
              </div>
              <div class="stat">
                <span class="stat-number">₹12L</span>
                <span class="stat-label">Current Salary</span>
              </div>
            </div>
          </div>
        </div>

        <div class="testimonial-card">
          <div class="testimonial-header">
            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&auto=format&q=80" alt="Rahul Patel" class="student-photo">
            <div class="student-info">
              <h3>Rahul Patel</h3>
              <p>Final Year Student | FEFU Class of 2025</p>
              <div class="student-location">
                <i class="fas fa-map-marker-alt"></i>
                <span>From: Gujarat | Currently: Vladivostok</span>
              </div>
            </div>
          </div>
          <div class="testimonial-content">
            <div class="quote-icon">"</div>
            <p>"The practical training at FEFU is exceptional. We get hands-on experience from the 3rd year itself. The professors are very supportive, and the Indian student community here is like a family. The cost is much lower than private colleges in India, and the quality is international standard."</p>
          </div>
        </div>

        <div class="testimonial-card">
          <div class="testimonial-header">
            <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&auto=format&q=80" alt="Sneha Reddy" class="student-photo">
            <div class="student-info">
              <h3>Sneha Reddy</h3>
              <p>3rd Year Student | FEFU Class of 2027</p>
              <div class="student-location">
                <i class="fas fa-map-marker-alt"></i>
                <span>From: Hyderabad | Currently: Vladivostok</span>
              </div>
            </div>
          </div>
          <div class="testimonial-content">
            <div class="quote-icon">"</div>
            <p>"Initially, I was worried about studying abroad, but FEFU has exceeded all my expectations. The campus is beautiful, the weather is manageable, and there's even Indian food available. My parents visit me every year, and they're impressed with the facilities and my progress."</p>
          </div>
        </div>
      </div>

      <div class="success-metrics">
        <h3>Our Track Record</h3>
        <div class="metrics-grid">
          <div class="metric-item">
            <div class="metric-number">95%</div>
            <div class="metric-label">MCI/NMC Screening Pass Rate</div>
          </div>
          <div class="metric-item">
            <div class="metric-number">2400+</div>
            <div class="metric-label">Indian Students Currently Enrolled</div>
          </div>
          <div class="metric-item">
            <div class="metric-number">650+</div>
            <div class="metric-label">Graduates Now Practicing in India</div>
          </div>
          <div class="metric-item">
            <div class="metric-number">100%</div>
            <div class="metric-label">Placement Assistance Provided</div>
          </div>
        </div>
      </div>
    </section>

    <section id="safety-support" class="section-padding safety-section">
      <h2 class="section-title">Student Safety & Support</h2>
      <p class="section-description">Comprehensive support system ensuring your child's safety, comfort, and academic success in Russia</p>

      <div class="safety-grid">
        <div class="safety-card">
          <div class="safety-icon">🛡️</div>
          <h3>Campus Security</h3>
          <ul>
            <li>24/7 security personnel on campus</li>
            <li>CCTV surveillance in all areas</li>
            <li>Secure dormitory access with key cards</li>
            <li>Emergency response system</li>
            <li>Safe campus environment with low crime rate</li>
          </ul>
        </div>

        <div class="safety-card">
          <div class="safety-icon">🏥</div>
          <h3>Medical Support</h3>
          <ul>
            <li>On-campus medical center with Indian doctors</li>
            <li>24/7 medical emergency services</li>
            <li>Health insurance coverage included</li>
            <li>Regular health check-ups</li>
            <li>Mental health counseling available</li>
          </ul>
        </div>

        <div class="safety-card">
          <div class="safety-icon">🍛</div>
          <h3>Indian Food & Culture</h3>
          <ul>
            <li>Indian mess with vegetarian options</li>
            <li>Halal food available</li>
            <li>Indian grocery stores nearby</li>
            <li>Festival celebrations (Diwali, Holi, etc.)</li>
            <li>Indian student associations active</li>
          </ul>
        </div>

        <div class="safety-card">
          <div class="safety-icon">📞</div>
          <h3>24/7 Support Helpline</h3>
          <ul>
            <li>Dedicated Indian support staff</li>
            <li>Hindi/English speaking coordinators</li>
            <li>Parent communication portal</li>
            <li>Emergency contact system</li>
            <li>Regular progress updates to parents</li>
          </ul>
        </div>

        <div class="safety-card">
          <div class="safety-icon">✈️</div>
          <h3>Travel & Visa Support</h3>
          <ul>
            <li>Complete visa application assistance</li>
            <li>Airport pickup and drop services</li>
            <li>Travel insurance guidance</li>
            <li>Annual India visit facilitation</li>
            <li>Emergency travel arrangements</li>
          </ul>
        </div>

        <div class="safety-card">
          <div class="safety-icon">🎓</div>
          <h3>Academic Mentorship</h3>
          <ul>
            <li>Senior Indian student mentors</li>
            <li>Regular academic counseling</li>
            <li>Study groups and peer support</li>
            <li>Exam preparation assistance</li>
            <li>Career guidance and placement help</li>
          </ul>
        </div>
      </div>

      <div class="parent-testimonial">
        <div class="parent-quote">
          <div class="quote-content">
            <div class="quote-icon">"</div>
            <p>"Initially, we were hesitant to send our daughter abroad for medical studies. But FEFU's support system and the Indian community there made us feel confident. The regular updates, safety measures, and academic progress reports gave us peace of mind. Our daughter is thriving there, and we couldn't be happier with our decision."</p>
            <div class="quote-author">
              <strong>Mrs. Sunita Agarwal</strong>
              <span>Mother of Priya Agarwal, 4th Year MBBS Student</span>
            </div>
          </div>
        </div>

        <div class="emergency-contacts">
          <h4>Emergency Contacts</h4>
          <div class="contact-grid">
            <div class="contact-item">
              <div class="contact-icon">🇮🇳</div>
              <div class="contact-info">
                <span class="contact-label">India Office</span>
                <span class="contact-number">+91 98765 43210</span>
              </div>
            </div>
            <div class="contact-item">
              <div class="contact-icon">🇷🇺</div>
              <div class="contact-info">
                <span class="contact-label">Russia Campus</span>
                <span class="contact-number">+7 423 XXX XXXX</span>
              </div>
            </div>
            <div class="contact-item">
              <div class="contact-icon">🚨</div>
              <div class="contact-info">
                <span class="contact-label">24/7 Emergency</span>
                <span class="contact-number">+91 98765 43211</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section id="about" class="section-padding about-fefu">
      <h2 class="section-title">About FEFU Medical School</h2>
      <p class="section-description">Far Eastern Federal University - Russia's gateway to Asia, ranked among top 5 universities</p>

      <div class="about-content">
        <div class="about-grid">
          <div class="about-card">
            <div class="about-icon">🏆</div>
            <h3>University Rankings</h3>
            <ul>
              <li>QS World University Rankings: 493rd globally</li>
              <li>Top 5 universities in Russia</li>
              <li>Leading medical education in Far East</li>
              <li>Member of Association of Pacific Rim Universities</li>
            </ul>
          </div>

          <div class="about-card">
            <div class="about-icon">🌍</div>
            <h3>Global Recognition</h3>
            <ul>
              <li>WHO World Directory of Medical Schools</li>
              <li>NMC (National Medical Commission) approved</li>
              <li>ECFMG certified for USMLE</li>
              <li>Recognized in 180+ countries worldwide</li>
            </ul>
          </div>

          <div class="about-card">
            <div class="about-icon">👨‍🎓</div>
            <h3>Student Community</h3>
            <ul>
              <li>40,000+ total students from 80+ countries</li>
              <li>2,000+ Indian students currently enrolled</li>
              <li>English-medium instruction throughout</li>
              <li>Strong alumni network in India</li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <section id="admission-process" class="section-padding admission-section">
      <h2 class="section-title">Admission Process & Requirements</h2>
      <p class="section-description">Step-by-step guide to secure your MBBS admission at FEFU</p>

      <div class="admission-content">
        <div class="process-timeline">
          <div class="timeline-item">
            <div class="timeline-number">1</div>
            <div class="timeline-content">
              <h3>Check Eligibility</h3>
              <ul>
                <li>Class 12th with PCB (50% for General, 40% for SC/ST/OBC)</li>
                <li>NEET qualification mandatory</li>
                <li>Age: 17-25 years as on 31st December</li>
                <li>Valid passport</li>
              </ul>
            </div>
          </div>

          <div class="timeline-item">
            <div class="timeline-number">2</div>
            <div class="timeline-content">
              <h3>Submit Application</h3>
              <ul>
                <li>Fill online application form</li>
                <li>Upload required documents</li>
                <li>Pay application fee: ₹10,000 ($120)</li>
                <li>Deadline: July 31st, 2025</li>
              </ul>
            </div>
          </div>

          <div class="timeline-item">
            <div class="timeline-number">3</div>
            <div class="timeline-content">
              <h3>Document Verification</h3>
              <ul>
                <li>Academic transcripts verification</li>
                <li>NEET scorecard validation</li>
                <li>Medical fitness certificate</li>
                <li>Processing time: 7-10 days</li>
              </ul>
            </div>
          </div>

          <div class="timeline-item">
            <div class="timeline-number">4</div>
            <div class="timeline-content">
              <h3>Admission Confirmation</h3>
              <ul>
                <li>Receive admission letter</li>
                <li>Pay first year fees</li>
                <li>Apply for student visa</li>
                <li>Book travel to Russia</li>
              </ul>
            </div>
          </div>
        </div>

        <div class="documents-checklist">
          <h3>Required Documents Checklist</h3>
          <div class="checklist-grid">
            <div class="checklist-column">
              <h4>Academic Documents</h4>
              <label class="checklist-item">
                <input type="checkbox" disabled>
                <span>Class 10th Mark Sheet</span>
              </label>
              <label class="checklist-item">
                <input type="checkbox" disabled>
                <span>Class 12th Mark Sheet</span>
              </label>
              <label class="checklist-item">
                <input type="checkbox" disabled>
                <span>NEET Scorecard</span>
              </label>
              <label class="checklist-item">
                <input type="checkbox" disabled>
                <span>School Leaving Certificate</span>
              </label>
            </div>

            <div class="checklist-column">
              <h4>Personal Documents</h4>
              <label class="checklist-item">
                <input type="checkbox" disabled>
                <span>Valid Passport</span>
              </label>
              <label class="checklist-item">
                <input type="checkbox" disabled>
                <span>Birth Certificate</span>
              </label>
              <label class="checklist-item">
                <input type="checkbox" disabled>
                <span>Medical Fitness Certificate</span>
              </label>
              <label class="checklist-item">
                <input type="checkbox" disabled>
                <span>Passport Size Photos (8 copies)</span>
              </label>
            </div>
          </div>

          <div class="important-dates">
            <h4>Important Dates 2025-26</h4>
            <div class="dates-grid">
              <div class="date-item">
                <span class="date">June 1</span>
                <span class="event">Application Opens</span>
              </div>
              <div class="date-item">
                <span class="date">July 31</span>
                <span class="event">Application Deadline</span>
              </div>
              <div class="date-item">
                <span class="date">August 15</span>
                <span class="event">Admission Results</span>
              </div>
              <div class="date-item">
                <span class="date">September 1</span>
                <span class="event">Classes Begin</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section id="why-fefu" class="section-padding" data-component="advantages">
      <h2 class="section-title">Why Choose FEFU?</h2>
      <p class="section-description">Discover the compelling advantages that make FEFU the ideal choice for your medical education.</p>
      <div id="advantages-container" class="advantages-container">
        <div class="component-loading">
          <div class="loading-spinner"></div>
        </div>
      </div>
    </section>

    <section id="faq" class="section-padding faq-section">
      <h2 class="section-title">Frequently Asked Questions</h2>
      <p class="section-description">Get answers to the most common questions from students and parents</p>

      <div class="faq-container">
        <div class="faq-item">
          <button class="faq-question" onclick="toggleFaq(this)">
            <span>Is FEFU degree valid for practicing in India?</span>
            <i class="fas fa-chevron-down"></i>
          </button>
          <div class="faq-answer">
            <p>Yes, FEFU is recognized by WHO and NMC (National Medical Commission). Graduates can appear for FMGE (Foreign Medical Graduate Examination) to practice in India. Our pass rate for FMGE is 95%.</p>
          </div>
        </div>

        <div class="faq-item">
          <button class="faq-question" onclick="toggleFaq(this)">
            <span>What is the medium of instruction?</span>
            <i class="fas fa-chevron-down"></i>
          </button>
          <div class="faq-answer">
            <p>The entire MBBS program is taught in English. All lectures, practicals, and examinations are conducted in English, making it easier for Indian students to understand and excel.</p>
          </div>
        </div>

        <div class="faq-item">
          <button class="faq-question" onclick="toggleFaq(this)">
            <span>How safe is it for Indian students in Russia?</span>
            <i class="fas fa-chevron-down"></i>
          </button>
          <div class="faq-answer">
            <p>Russia is very safe for international students. FEFU campus has 24/7 security, CCTV surveillance, and a large Indian student community. The crime rate in Vladivostok is very low, and the local people are friendly towards Indian students.</p>
          </div>
        </div>

        <div class="faq-item">
          <button class="faq-question" onclick="toggleFaq(this)">
            <span>What about food and accommodation?</span>
            <i class="fas fa-chevron-down"></i>
          </button>
          <div class="faq-answer">
            <p>FEFU provides comfortable dormitories with 2-3 students per room. Indian mess facilities are available on campus with vegetarian and non-vegetarian options. Indian grocery stores and restaurants are also available nearby.</p>
          </div>
        </div>

        <div class="faq-item">
          <button class="faq-question" onclick="toggleFaq(this)">
            <span>What is the total cost including living expenses?</span>
            <i class="fas fa-chevron-down"></i>
          </button>
          <div class="faq-answer">
            <p>The total cost for 6 years including tuition, accommodation, and living expenses ranges from ₹42-48 lakhs (2025 rates). This is significantly lower than private medical colleges in India which charge ₹1-2 crores.</p>
          </div>
        </div>

        <div class="faq-item">
          <button class="faq-question" onclick="toggleFaq(this)">
            <span>Can parents visit during the course?</span>
            <i class="fas fa-chevron-down"></i>
          </button>
          <div class="faq-answer">
            <p>Yes, parents can visit Russia on a tourist visa. We assist with invitation letters and travel arrangements. Many parents visit annually, and we organize parent-student meetings during their visits.</p>
          </div>
        </div>

        <div class="faq-item">
          <button class="faq-question" onclick="toggleFaq(this)">
            <span>What happens after graduation?</span>
            <i class="fas fa-chevron-down"></i>
          </button>
          <div class="faq-answer">
            <p>After graduation, students can return to India and appear for FMGE to practice in India. Alternatively, they can pursue further studies (MD/MS) in Russia or other countries. We provide complete guidance for both options.</p>
          </div>
        </div>

        <div class="faq-item">
          <button class="faq-question" onclick="toggleFaq(this)">
            <span>Is NEET required for admission?</span>
            <i class="fas fa-chevron-down"></i>
          </button>
          <div class="faq-answer">
            <p>Yes, NEET qualification is mandatory for Indian students as per NMC guidelines. Students must have appeared for NEET and obtained a qualifying score to be eligible for admission to FEFU.</p>
          </div>
        </div>
      </div>
    </section>

    <section id="apply" class="section-padding" data-component="apply-button">
      <h2 class="section-title">Begin Your Medical Journey</h2>
      <p class="section-description">Take the first step toward your medical career at one of Russia's top universities.</p>
      <div id="apply-button-container" class="apply-button-container">
        <div class="component-loading">
          <div class="loading-spinner"></div>
        </div>
      </div>
      
      <div class="cost-calculator-section">
        <h3>Calculate Your Total Investment</h3>
        <p>Get a personalized estimate of your complete MBBS education cost at FEFU</p>

        <div class="calculator-container">
          <div class="calculator-inputs">
            <div class="input-group">
              <label for="program-duration">Program Duration</label>
              <select id="program-duration">
                <option value="6">6 Years (Standard MBBS)</option>
                <option value="5">5 Years (Fast Track - if available)</option>
              </select>
            </div>

            <div class="input-group">
              <label for="accommodation-type">Accommodation Type</label>
              <select id="accommodation-type">
                <option value="shared">Shared Room (₹1.25L/year)</option>
                <option value="single">Single Room (₹1.8L/year)</option>
                <option value="off-campus">Off-Campus Housing (₹1.5L/year)</option>
              </select>
            </div>

            <div class="input-group">
              <label for="lifestyle">Lifestyle & Expenses</label>
              <select id="lifestyle">
                <option value="basic">Basic (₹6,500/month)</option>
                <option value="moderate">Moderate (₹10,000/month)</option>
                <option value="comfortable">Comfortable (₹15,000/month)</option>
              </select>
            </div>

            <button id="calculate-cost" class="calculate-btn">
              <i class="fas fa-calculator"></i>
              Calculate Total Cost
            </button>
          </div>

          <div class="calculator-results" id="cost-results" style="display: none;">
            <h4>Your Investment Breakdown</h4>
            <div class="cost-breakdown">
              <div class="cost-item">
                <span class="cost-label">Tuition Fees (6 years)</span>
                <span class="cost-value" id="tuition-cost">₹27,00,000</span>
              </div>
              <div class="cost-item">
                <span class="cost-label">Accommodation</span>
                <span class="cost-value" id="accommodation-cost">₹7,50,000</span>
              </div>
              <div class="cost-item">
                <span class="cost-label">Living Expenses</span>
                <span class="cost-value" id="living-cost">₹4,80,000</span>
              </div>
              <div class="cost-item">
                <span class="cost-label">One-time Fees</span>
                <span class="cost-value">₹2,00,000</span>
              </div>
              <div class="cost-item total">
                <span class="cost-label">Total Investment</span>
                <span class="cost-value" id="total-cost">₹41,30,000</span>
              </div>
            </div>

            <div class="comparison-note">
              <p><strong>💡 Smart Investment:</strong> This is 55-65% less than private medical colleges in India (₹1Cr-₹2Cr in 2025)</p>
            </div>

            <div class="payment-options">
              <h5>Flexible Payment Options</h5>
              <div class="payment-grid">
                <div class="payment-option">
                  <div class="payment-icon">💳</div>
                  <h6>Annual Payment</h6>
                  <p>Pay year by year</p>
                </div>
                <div class="payment-option">
                  <div class="payment-icon">🏦</div>
                  <h6>Education Loan</h6>
                  <p>Bank loans available</p>
                </div>
                <div class="payment-option">
                  <div class="payment-icon">📅</div>
                  <h6>Installments</h6>
                  <p>Semester-wise payment</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="application-form">
        <h3>Start Your Medical Journey</h3>
        <p>Fill out this form and our education consultants will contact you within 24 hours to discuss your MBBS admission at FEFU.</p>

        <form id="enquiry-form" novalidate>
          <div class="form-row">
            <div class="form-group">
              <label for="name">Full Name</label>
              <input type="text" id="name" name="name" required aria-describedby="name-error" placeholder="Enter your full name">
              <span class="error-message" id="name-error"></span>
            </div>

            <div class="form-group">
              <label for="email">Email Address</label>
              <input type="email" id="email" name="email" required aria-describedby="email-error" placeholder="<EMAIL>">
              <span class="error-message" id="email-error"></span>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="phone">Phone Number</label>
              <input type="tel" id="phone" name="phone" required aria-describedby="phone-error" placeholder="+91 XXXXX XXXXX">
              <span class="error-message" id="phone-error"></span>
            </div>

            <div class="form-group">
              <label for="qualification">Current Qualification</label>
              <select id="qualification" name="qualification">
                <option value="">Select your qualification</option>
                <option value="12th-appearing">12th Standard - Appearing</option>
                <option value="12th-completed">12th Standard - Completed</option>
                <option value="graduate">Graduate Degree</option>
                <option value="other">Other Qualification</option>
              </select>
            </div>
          </div>

          <div class="form-group">
            <label for="message">Tell us about your goals</label>
            <textarea id="message" name="message" rows="4" placeholder="Share your medical career aspirations, any specific questions about FEFU, or additional information you'd like us to know..."></textarea>
          </div>

          <button type="submit" class="submit-btn">
            <span>Submit Enquiry</span>
            <i class="fas fa-paper-plane" aria-hidden="true"></i>
          </button>
        </form>
      </div>
    </section>
  </main>

  <footer role="contentinfo">
    <div class="footer-content">
      <div class="footer-section">
        <h3>MBBS Vostrix</h3>
        <p>Your trusted partner for medical education at FEFU, Russia. We guide students through every step of their journey to becoming qualified doctors with personalized support and expert guidance.</p>

        <div class="newsletter-signup">
          <h4>Stay Updated</h4>
          <div class="newsletter-form">
            <input type="email" placeholder="Enter your email" aria-label="Email for newsletter">
            <button type="button">Subscribe</button>
          </div>
        </div>
      </div>

      <div class="footer-section">
        <h4>Quick Links</h4>
        <ul>
          <li><a href="#about">About FEFU</a></li>
          <li><a href="#program">MBBS Program</a></li>
          <li><a href="#campus">Campus Life</a></li>
          <li><a href="#why-fefu">Why Choose Us</a></li>
          <li><a href="#faq">FAQ</a></li>
          <li><a href="#apply">Apply Now</a></li>
        </ul>
      </div>

      <div class="footer-section">
        <h4>Contact Info</h4>
        <div class="contact-info">
          <p><i class="fas fa-phone" aria-hidden="true"></i> <a href="tel:+************" class="contact-link">+91 98765 43210</a></p>
          <p><i class="fas fa-whatsapp" aria-hidden="true"></i> <a href="https://wa.me/************" class="contact-link whatsapp-link" target="_blank">WhatsApp Chat</a></p>
          <p><i class="fas fa-envelope" aria-hidden="true"></i> <a href="mailto:<EMAIL>" class="contact-link"><EMAIL></a></p>
          <p><i class="fas fa-map-marker-alt" aria-hidden="true"></i> 304, Lotus Corporate Park, Goregaon (E), Mumbai - 400063</p>
          <p><i class="fas fa-clock" aria-hidden="true"></i> Mon-Sat: 9AM-7PM IST | <span class="emergency-contact">24/7 Emergency: +91 98765 43211</span></p>
        </div>
      </div>

      <div class="footer-section">
        <h4>Follow Us</h4>
        <div class="social-links">
          <a href="#" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
          <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
          <a href="#" aria-label="YouTube"><i class="fab fa-youtube"></i></a>
          <a href="#" aria-label="LinkedIn"><i class="fab fa-linkedin-in"></i></a>
          <a href="#" aria-label="WhatsApp"><i class="fab fa-whatsapp"></i></a>
        </div>

        <div style="margin-top: 1.5rem; padding: 1rem; background: rgba(255,255,255,0.05); border-radius: 8px; border: 1px solid rgba(255,255,255,0.1);">
          <p style="margin: 0; font-size: 0.875rem; color: #d1d5db;">
            <i class="fas fa-shield-alt" style="color: #10b981; margin-right: 0.5rem;"></i>
            WHO & NMC Recognized University
          </p>
        </div>
      </div>
    </div>

    <div class="footer-bottom">
      <p>&copy; 2025 MBBS Vostrix. All rights reserved. | <a href="#privacy">Privacy Policy</a> | <a href="#terms">Terms of Service</a> | <a href="#disclaimer">Disclaimer</a></p>
    </div>
  </footer>

  <!-- Mobile Sticky CTA -->
  <div class="mobile-sticky-cta" id="mobile-sticky-cta">
    <div class="sticky-cta-content">
      <div class="cta-text">
        <span class="cta-title">Ready to Apply?</span>
        <span class="cta-subtitle">Start your MBBS journey</span>
      </div>
      <a href="#apply" class="sticky-apply-btn">
        Apply Now
        <i class="fas fa-arrow-right"></i>
      </a>
    </div>
  </div>

  <!-- Back to Top Button -->
  <button class="back-to-top" id="backToTop" aria-label="Back to top">
    <i class="fas fa-chevron-up"></i>
  </button>

  <!-- No-module components script -->
  <script src="js/no-module-components.js"></script>
  
  <!-- Debug functionality -->
  <script>
    // Show debug button if ?debug=true
    if (window.location.search.includes('debug')) {
      document.getElementById('debug-btn').style.display = 'block';
    }
    
    // Global function to clear loading states
    function clearAllLoadingStates() {
      const loadingElements = document.querySelectorAll('.component-loading');
      console.log('Manually clearing', loadingElements.length, 'loading states');
      loadingElements.forEach((el, index) => {
        console.log(`Removing loading element ${index + 1}`);
        el.style.opacity = '0';
        setTimeout(() => el.remove(), 300);
      });
    }
    
    // Auto-clear loading states after 10 seconds as ultimate fallback
    setTimeout(() => {
      const remainingLoading = document.querySelectorAll('.component-loading');
      if (remainingLoading.length > 0) {
        console.log('Auto-clearing remaining loading states after 10 seconds');
        clearAllLoadingStates();
      }
    }, 10000);
    
    // Cost Calculator Functionality
    document.addEventListener('DOMContentLoaded', function() {
      const calculateBtn = document.getElementById('calculate-cost');
      const resultsDiv = document.getElementById('cost-results');

      if (calculateBtn) {
        calculateBtn.addEventListener('click', function() {
          const duration = parseInt(document.getElementById('program-duration').value);
          const accommodation = document.getElementById('accommodation-type').value;
          const lifestyle = document.getElementById('lifestyle').value;

          // Calculate costs (2025 rates)
          const tuitionPerYear = 450000; // ₹4.5L per year (2025 rate)
          const tuitionTotal = tuitionPerYear * duration;

          let accommodationPerYear;
          switch(accommodation) {
            case 'shared': accommodationPerYear = 125000; break; // ₹1.25L
            case 'single': accommodationPerYear = 180000; break; // ₹1.8L
            case 'off-campus': accommodationPerYear = 150000; break; // ₹1.5L
          }
          const accommodationTotal = accommodationPerYear * duration;

          let livingPerYear;
          switch(lifestyle) {
            case 'basic': livingPerYear = 80000; break; // ₹80K
            case 'moderate': livingPerYear = 120000; break; // ₹1.2L
            case 'comfortable': livingPerYear = 180000; break; // ₹1.8L
          }
          const livingTotal = livingPerYear * duration;

          const oneTimeFees = 200000; // ₹2L (2025 rate)
          const grandTotal = tuitionTotal + accommodationTotal + livingTotal + oneTimeFees;

          // Update display
          document.getElementById('tuition-cost').textContent = `₹${(tuitionTotal/100000).toFixed(1)}L`;
          document.getElementById('accommodation-cost').textContent = `₹${(accommodationTotal/100000).toFixed(1)}L`;
          document.getElementById('living-cost').textContent = `₹${(livingTotal/100000).toFixed(1)}L`;
          document.getElementById('total-cost').textContent = `₹${(grandTotal/100000).toFixed(1)}L`;

          // Show results with animation
          resultsDiv.style.display = 'block';
          resultsDiv.scrollIntoView({ behavior: 'smooth', block: 'nearest' });

          // Add animation class
          resultsDiv.style.opacity = '0';
          resultsDiv.style.transform = 'translateY(20px)';
          setTimeout(() => {
            resultsDiv.style.transition = 'all 0.5s ease';
            resultsDiv.style.opacity = '1';
            resultsDiv.style.transform = 'translateY(0)';
          }, 100);
        });
      }

      // Enhanced form handling with modern UX
      const form = document.getElementById('enquiry-form');
      if (!form) return;

      // Form validation functions
      function validateField(field) {
        const value = field.value.trim();
        const fieldGroup = field.closest('.form-group');
        const errorElement = fieldGroup.querySelector('.error-message');

        // Remove existing states
        fieldGroup.classList.remove('error', 'success');

        let isValid = true;
        let errorMessage = '';

        // Field-specific validation
        switch (field.type) {
          case 'text':
            if (!value) {
              errorMessage = 'This field is required';
              isValid = false;
            } else if (value.length < 2) {
              errorMessage = 'Please enter a valid name';
              isValid = false;
            }
            break;

          case 'email':
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!value) {
              errorMessage = 'Email address is required';
              isValid = false;
            } else if (!emailRegex.test(value)) {
              errorMessage = 'Please enter a valid email address';
              isValid = false;
            }
            break;

          case 'tel':
            const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
            if (!value) {
              errorMessage = 'Phone number is required';
              isValid = false;
            } else if (!phoneRegex.test(value.replace(/[\s\-\(\)]/g, ''))) {
              errorMessage = 'Please enter a valid phone number';
              isValid = false;
            }
            break;
        }

        // Update UI based on validation
        if (isValid) {
          fieldGroup.classList.add('success');
          errorElement.textContent = '';
        } else {
          fieldGroup.classList.add('error');
          errorElement.textContent = errorMessage;
        }

        return isValid;
      }

      // Real-time validation
      const requiredFields = form.querySelectorAll('input[required]');
      requiredFields.forEach(field => {
        field.addEventListener('blur', () => validateField(field));
        field.addEventListener('input', () => {
          if (field.closest('.form-group').classList.contains('error')) {
            validateField(field);
          }
        });
      });

      // Form submission
      form.addEventListener('submit', function(e) {
        e.preventDefault();

        // Validate all required fields
        let isFormValid = true;
        requiredFields.forEach(field => {
          if (!validateField(field)) {
            isFormValid = false;
          }
        });

        if (!isFormValid) {
          // Focus on first error field
          const firstError = form.querySelector('.form-group.error input');
          if (firstError) {
            firstError.focus();
          }
          return;
        }

        // Show loading state
        const submitBtn = form.querySelector('.submit-btn');
        const originalContent = submitBtn.innerHTML;
        submitBtn.classList.add('loading');
        submitBtn.disabled = true;

        // Simulate form submission
        setTimeout(() => {
          // Show success message
          showSuccessMessage();

          // Reset form
          form.reset();
          form.querySelectorAll('.form-group').forEach(group => {
            group.classList.remove('error', 'success');
          });

          // Reset button
          submitBtn.classList.remove('loading');
          submitBtn.disabled = false;
          submitBtn.innerHTML = originalContent;
        }, 2000);
      });

      function showSuccessMessage() {
        const message = document.createElement('div');
        message.className = 'success-toast';
        message.innerHTML = `
          <div class="toast-content">
            <i class="fas fa-check-circle"></i>
            <div>
              <h4>Enquiry Submitted Successfully!</h4>
              <p>Our education consultant will contact you within 24 hours.</p>
            </div>
          </div>
        `;

        // Add toast styles
        message.style.cssText = `
          position: fixed;
          top: 20px;
          right: 20px;
          background: linear-gradient(135deg, #10b981, #059669);
          color: white;
          padding: 1rem 1.5rem;
          border-radius: 12px;
          box-shadow: 0 10px 25px rgba(0,0,0,0.2);
          z-index: 10000;
          transform: translateX(100%);
          transition: transform 0.3s ease;
          max-width: 400px;
        `;

        document.body.appendChild(message);

        // Animate in
        setTimeout(() => {
          message.style.transform = 'translateX(0)';
        }, 100);

        // Auto remove
        setTimeout(() => {
          message.style.transform = 'translateX(100%)';
          setTimeout(() => message.remove(), 300);
        }, 5000);
      }

      // Mobile sticky CTA functionality
      const mobileStickyCta = document.getElementById('mobile-sticky-cta');
      const applySection = document.getElementById('apply');

      if (mobileStickyCta && applySection) {
        window.addEventListener('scroll', () => {
          const applySectionTop = applySection.offsetTop;
          const scrollPosition = window.pageYOffset + window.innerHeight;

          // Show sticky CTA when user scrolls past hero section but before apply section
          if (window.pageYOffset > window.innerHeight * 0.5 && scrollPosition < applySectionTop + 100) {
            mobileStickyCta.classList.add('visible');
          } else {
            mobileStickyCta.classList.remove('visible');
          }
        });
      }

      // Back to top functionality
      const backToTopBtn = document.getElementById('backToTop');
      if (backToTopBtn) {
        window.addEventListener('scroll', () => {
          if (window.pageYOffset > 300) {
            backToTopBtn.classList.add('visible');
          } else {
            backToTopBtn.classList.remove('visible');
          }
        });

        backToTopBtn.addEventListener('click', () => {
          window.scrollTo({
            top: 0,
            behavior: 'smooth'
          });
        });
      }

      // Newsletter subscription
      const newsletterForm = document.querySelector('.newsletter-form');
      if (newsletterForm) {
        const emailInput = newsletterForm.querySelector('input[type="email"]');
        const subscribeBtn = newsletterForm.querySelector('button');

        subscribeBtn.addEventListener('click', () => {
          const email = emailInput.value.trim();
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

          if (!email) {
            emailInput.style.borderColor = '#ef4444';
            emailInput.focus();
            return;
          }

          if (!emailRegex.test(email)) {
            emailInput.style.borderColor = '#ef4444';
            emailInput.focus();
            return;
          }

          // Success state
          emailInput.style.borderColor = '#10b981';
          subscribeBtn.textContent = 'Subscribed!';
          subscribeBtn.style.background = '#10b981';
          subscribeBtn.disabled = true;

          setTimeout(() => {
            emailInput.value = '';
            emailInput.style.borderColor = '';
            subscribeBtn.textContent = 'Subscribe';
            subscribeBtn.style.background = '';
            subscribeBtn.disabled = false;
          }, 3000);
        });

        emailInput.addEventListener('input', () => {
          emailInput.style.borderColor = '';
        });
      }

      // Smooth scrolling for navigation links
      document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
          e.preventDefault();
          const target = document.querySelector(this.getAttribute('href'));
          if (target) {
            const headerOffset = 80;
            const elementPosition = target.offsetTop;
            const offsetPosition = elementPosition - headerOffset;

            window.scrollTo({
              top: offsetPosition,
              behavior: 'smooth'
            });
          }
        });
      });

      // Add loading animation to CTA buttons
      document.querySelectorAll('.cta-button, .apply-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
          if (this.getAttribute('href') === '#apply') {
            // Add ripple effect
            const ripple = document.createElement('span');
            ripple.style.cssText = `
              position: absolute;
              border-radius: 50%;
              background: rgba(255,255,255,0.6);
              transform: scale(0);
              animation: ripple 0.6s linear;
              pointer-events: none;
            `;

            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = (e.clientX - rect.left - size / 2) + 'px';
            ripple.style.top = (e.clientY - rect.top - size / 2) + 'px';

            this.appendChild(ripple);

            setTimeout(() => ripple.remove(), 600);
          }
        });
      });

      // Add CSS for ripple animation
      const rippleStyle = document.createElement('style');
      rippleStyle.textContent = `
        @keyframes ripple {
          to {
            transform: scale(4);
            opacity: 0;
          }
        }

        .toast-content {
          display: flex;
          align-items: center;
          gap: 1rem;
        }

        .toast-content i {
          font-size: 1.5rem;
          color: #ffffff;
        }

        .toast-content h4 {
          margin: 0 0 0.25rem 0;
          font-size: 1rem;
          font-weight: 600;
        }

        .toast-content p {
          margin: 0;
          font-size: 0.875rem;
          opacity: 0.9;
        }
      `;
      document.head.appendChild(rippleStyle);
    });

    // FAQ Toggle Functionality
    function toggleFaq(button) {
      const faqItem = button.closest('.faq-item');
      const isActive = faqItem.classList.contains('active');

      // Close all FAQ items
      document.querySelectorAll('.faq-item').forEach(item => {
        item.classList.remove('active');
      });

      // Open clicked item if it wasn't active
      if (!isActive) {
        faqItem.classList.add('active');
      }
    }
  </script>
</body>
</html>
