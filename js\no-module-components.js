// Non-module version of components that works without CORS issues
// This file can be loaded directly with <script> tags

// Simple Campus Gallery (No modules required)
function createSimpleCampusGalleryNoModule(container) {
  console.log('Creating simple campus gallery (no-module)...');
  
  const images = [
    {
      url: 'https://images.unsplash.com/photo-**********-701939374585?w=400&h=300&fit=crop&auto=format&q=80',
      title: 'FEFU Main Campus',
      description: 'Modern campus facilities on Russky Island'
    },
    {
      url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400&h=300&fit=crop&auto=format&q=80',
      title: 'Medical Faculty Building',
      description: 'State-of-the-art medical education facilities'
    },
    {
      url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=300&fit=crop&auto=format&q=80',
      title: 'Student Dormitories',
      description: 'Comfortable accommodation for international students'
    },
    {
      url: 'https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=400&h=300&fit=crop&auto=format&q=80',
      title: 'Laboratory Facilities',
      description: 'Advanced medical research and training labs'
    }
  ];

  // Clear container and create simple gallery
  container.innerHTML = `
    <div class="simple-gallery">
      <div class="gallery-header">
        <h3>Campus Gallery</h3>
        <p>Explore FEFU's modern facilities</p>
      </div>
      <div class="gallery-grid">
        ${images.map((img, index) => `
          <div class="gallery-item" data-index="${index}">
            <img src="${img.url}" alt="${img.title}" loading="lazy">
            <div class="gallery-item-info">
              <h4>${img.title}</h4>
              <p>${img.description}</p>
            </div>
          </div>
        `).join('')}
      </div>
    </div>
  `;

  // Add click handlers
  const galleryItems = container.querySelectorAll('.gallery-item');
  galleryItems.forEach((item, index) => {
    item.addEventListener('click', () => {
      showSimpleModalNoModule(images[index]);
    });
    
    // Add hover effects
    item.addEventListener('mouseenter', () => {
      item.style.transform = 'translateY(-5px)';
      item.style.boxShadow = '0 10px 25px rgba(0,0,0,0.15)';
    });
    
    item.addEventListener('mouseleave', () => {
      item.style.transform = 'translateY(0)';
      item.style.boxShadow = '0 4px 15px rgba(0,0,0,0.1)';
    });
  });

  console.log('✅ Simple campus gallery created successfully (no-module)');
  return {
    type: 'simple-no-module',
    container,
    images
  };
}

// Simple Apply Button (No modules required)
function createSimpleApplyButtonNoModule(container) {
  console.log('Creating simple apply button (no-module)...');
  
  container.innerHTML = `
    <div class="simple-apply-button">
      <div class="apply-content">
        <div class="apply-icon">
          <i class="fas fa-graduation-cap"></i>
        </div>
        <h3>Ready to Start Your Medical Journey?</h3>
        <p>Join thousands of students at FEFU's world-class medical program</p>
        <button class="apply-btn" id="main-apply-btn-no-module">
          <span>Apply Now</span>
          <i class="fas fa-arrow-right"></i>
        </button>
        <div class="apply-features">
          <div class="feature">
            <i class="fas fa-check"></i>
            <span>WHO & NMC Recognized</span>
          </div>
          <div class="feature">
            <i class="fas fa-check"></i>
            <span>English Medium</span>
          </div>
          <div class="feature">
            <i class="fas fa-check"></i>
            <span>Affordable Fees</span>
          </div>
        </div>
      </div>
    </div>
  `;

  // Add button functionality
  const applyBtn = container.querySelector('#main-apply-btn-no-module');
  if (applyBtn) {
    applyBtn.addEventListener('click', () => {
      // Add click animation
      applyBtn.style.transform = 'scale(0.95)';
      setTimeout(() => {
        applyBtn.style.transform = 'scale(1)';
      }, 150);
      
      // Scroll to form
      const form = document.querySelector('.application-form');
      if (form) {
        form.scrollIntoView({ behavior: 'smooth', block: 'center' });
        
        // Focus first input after scroll
        setTimeout(() => {
          const firstInput = form.querySelector('input');
          if (firstInput) firstInput.focus();
        }, 1000);
      }
      
      // Track click
      console.log('Apply button clicked (no-module)');
      if (typeof gtag !== 'undefined') {
        gtag('event', 'apply_button_click', {
          'event_category': 'conversion',
          'event_label': 'no_module_apply_button'
        });
      }
    });

    // Add hover effects
    applyBtn.addEventListener('mouseenter', () => {
      applyBtn.style.transform = 'translateY(-2px)';
      applyBtn.style.boxShadow = '0 8px 25px rgba(0, 86, 179, 0.3)';
    });
    
    applyBtn.addEventListener('mouseleave', () => {
      applyBtn.style.transform = 'translateY(0)';
      applyBtn.style.boxShadow = '0 4px 15px rgba(0, 86, 179, 0.2)';
    });
  }

  console.log('✅ Simple apply button created successfully (no-module)');
  return {
    type: 'simple-no-module',
    container
  };
}

// Simple Info Cards (No modules required)
function createSimpleInfoCardsNoModule(container) {
  console.log('Creating simple info cards (no-module)...');

  const infoCards = [
    {
      icon: '💰',
      title: 'Fees & Costs',
      details: [
        'Tuition Fee: ₹3.8L - ₹4.2L per year',
        'Hostel Fee: ₹80,000 - ₹1.2L per year',
        'Living Expenses: ₹60,000 - ₹80,000 per year',
        'One-time Admission: ₹50,000',
        'Total Annual Cost: ₹5.5L - ₹6.5L'
      ],
      color: '#28a745',
      highlight: 'Most Affordable in Europe'
    },
    {
      icon: '🏠',
      title: 'Accommodation',
      details: [
        'Modern dormitories on campus',
        '2-3 students per room with attached bathroom',
        'Wi-Fi, heating, and 24/7 security',
        'Indian mess facility available',
        'Laundry and recreational facilities'
      ],
      color: '#0056b3',
      highlight: 'Safe & Comfortable Living'
    },
    {
      icon: '📋',
      title: 'Eligibility',
      details: [
        'Class 12th with PCB (50% for General, 40% for SC/ST/OBC)',
        'NEET qualification mandatory',
        'Age: 17-25 years as on 31st December',
        'English proficiency (basic level)',
        'No donation or capitation fees'
      ],
      color: '#6f42c1',
      highlight: 'NEET Qualified Students'
    },
    {
      icon: '🎓',
      title: 'Program Structure',
      details: [
        '6 years total duration (5+1 internship)',
        'First 5 years: Pre-clinical + Clinical',
        'Year 1-2: Basic medical sciences',
        'Year 3-5: Clinical rotations',
        'Year 6: Mandatory internship'
      ],
      color: '#dc3545',
      highlight: 'WHO & NMC Recognized'
    }
  ];

  container.innerHTML = `
    <div class="simple-info-cards">
      <div class="info-cards-grid">
        ${infoCards.map((card, index) => `
          <div class="info-card" data-index="${index}" style="animation-delay: ${index * 0.2}s">
            <div class="card-header" style="background: ${card.color}">
              <div class="card-icon">${card.icon}</div>
              <h3>${card.title}</h3>
              <span class="card-highlight">${card.highlight}</span>
            </div>
            <div class="card-content">
              <ul class="card-details">
                ${card.details.map(detail => `<li>${detail}</li>`).join('')}
              </ul>
            </div>
          </div>
        `).join('')}
      </div>
      <div class="info-cards-footer">
        <p><strong>Note:</strong> All fees are approximate and subject to annual revision. Contact us for the most current fee structure.</p>
        <button class="download-brochure-btn">
          <i class="fas fa-download"></i>
          Download Detailed Brochure
        </button>
      </div>
    </div>
  `;

  // Add hover effects and interactions
  const cards = container.querySelectorAll('.info-card');
  cards.forEach(card => {
    card.addEventListener('mouseenter', () => {
      card.style.transform = 'translateY(-8px) scale(1.02)';
      card.style.boxShadow = '0 15px 35px rgba(0,0,0,0.15)';
    });

    card.addEventListener('mouseleave', () => {
      card.style.transform = 'translateY(0) scale(1)';
      card.style.boxShadow = '0 5px 20px rgba(0,0,0,0.1)';
    });
  });

  // Download brochure functionality
  const downloadBtn = container.querySelector('.download-brochure-btn');
  if (downloadBtn) {
    downloadBtn.addEventListener('click', () => {
      // Simulate download and track conversion
      console.log('Brochure download requested');
      downloadBtn.innerHTML = '<i class="fas fa-check"></i> Request Sent!';
      downloadBtn.style.background = '#28a745';

      setTimeout(() => {
        downloadBtn.innerHTML = '<i class="fas fa-download"></i> Download Detailed Brochure';
        downloadBtn.style.background = '';
      }, 3000);
    });
  }

  console.log('✅ Simple info cards created successfully (no-module)');
  return {
    type: 'simple-no-module',
    container,
    infoCards
  };
}

// Simple Advantages (No modules required)
function createSimpleAdvantagesNoModule(container) {
  console.log('Creating simple advantages (no-module)...');
  
  const advantages = [
    {
      icon: '🏆',
      title: 'Top 5 in Russia',
      description: 'FEFU ranks among the top 5 universities in Russia and is featured in QS World University Rankings.',
      color: '#FFD700'
    },
    {
      icon: '🌍',
      title: 'Global Recognition',
      description: 'WHO and NMC recognized degree allows you to practice medicine worldwide.',
      color: '#0056b3'
    },
    {
      icon: '💰',
      title: 'Affordable Excellence',
      description: 'High-quality medical education at a fraction of the cost compared to private colleges.',
      color: '#28a745'
    },
    {
      icon: '👨‍⚕️',
      title: 'Expert Faculty',
      description: 'Learn from experienced professors and practicing physicians with international expertise.',
      color: '#6f42c1'
    }
  ];

  container.innerHTML = `
    <div class="simple-advantages">
      <div class="advantages-grid">
        ${advantages.map((advantage, index) => `
          <div class="advantage-item" data-index="${index}" style="animation-delay: ${index * 0.2}s">
            <div class="advantage-icon" style="background: ${advantage.color}">
              ${advantage.icon}
            </div>
            <h4>${advantage.title}</h4>
            <p>${advantage.description}</p>
          </div>
        `).join('')}
      </div>
    </div>
  `;

  // Add hover effects
  const advantageItems = container.querySelectorAll('.advantage-item');
  advantageItems.forEach(item => {
    item.addEventListener('mouseenter', () => {
      item.style.transform = 'translateY(-5px) scale(1.02)';
      item.style.boxShadow = '0 10px 25px rgba(0,0,0,0.15)';
    });
    
    item.addEventListener('mouseleave', () => {
      item.style.transform = 'translateY(0) scale(1)';
      item.style.boxShadow = '0 4px 15px rgba(0,0,0,0.1)';
    });
  });

  console.log('✅ Simple advantages created successfully (no-module)');
  return {
    type: 'simple-no-module',
    container,
    advantages
  };
}

// Modal function (No modules required)
function showSimpleModalNoModule(imageData) {
  const modal = document.createElement('div');
  modal.className = 'simple-modal';
  modal.innerHTML = `
    <div class="modal-content">
      <button class="modal-close" aria-label="Close">&times;</button>
      <img src="${imageData.url}" alt="${imageData.title}">
      <div class="modal-info">
        <h3>${imageData.title}</h3>
        <p>${imageData.description}</p>
      </div>
    </div>
  `;

  document.body.appendChild(modal);

  // Close handlers
  const closeBtn = modal.querySelector('.modal-close');
  closeBtn.addEventListener('click', () => modal.remove());
  modal.addEventListener('click', (e) => {
    if (e.target === modal) modal.remove();
  });
  
  // Keyboard close
  const handleKeydown = (e) => {
    if (e.key === 'Escape') {
      modal.remove();
      document.removeEventListener('keydown', handleKeydown);
    }
  };
  document.addEventListener('keydown', handleKeydown);

  // Focus management
  closeBtn.focus();
}

// Inject styles function (No modules required)
function injectSimpleStylesNoModule() {
  if (document.getElementById('simple-components-styles-no-module')) return;

  // The styles are now handled by the modern CSS files
  // This function is kept for compatibility but styles are externalized
  console.log('✅ Modern styles are loaded via external CSS files');

  // Add any component-specific dynamic styles here if needed
  const styles = document.createElement('style');
  styles.id = 'simple-components-styles-no-module';
  styles.textContent = `
    .simple-gallery {
      padding: 2rem;
    }
    
    .gallery-header {
      text-align: center;
      margin-bottom: 2rem;
    }
    
    .gallery-header h3 {
      font-size: 2rem;
      color: #0056b3;
      margin-bottom: 0.5rem;
    }
    
    .gallery-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 1.5rem;
    }
    
    .gallery-item {
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
      transition: all 0.3s ease;
      cursor: pointer;
    }
    
    .gallery-item img {
      width: 100%;
      height: 200px;
      object-fit: cover;
    }
    
    .gallery-item-info {
      padding: 1rem;
    }
    
    .gallery-item-info h4 {
      color: #0056b3;
      margin-bottom: 0.5rem;
    }
    
    .simple-apply-button {
      background: linear-gradient(135deg, #0056b3, #004494);
      border-radius: 12px;
      padding: 3rem 2rem;
      text-align: center;
      color: white;
    }
    
    .apply-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
      color: #FFD700;
    }
    
    .apply-content h3 {
      font-size: 2rem;
      margin-bottom: 1rem;
    }
    
    .apply-content p {
      font-size: 1.1rem;
      margin-bottom: 2rem;
      opacity: 0.9;
    }
    
    .apply-btn {
      background: #FFD700;
      color: #0056b3;
      border: none;
      padding: 1rem 2rem;
      border-radius: 8px;
      font-size: 1.2rem;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 2rem;
    }
    
    .apply-features {
      display: flex;
      justify-content: center;
      gap: 2rem;
      flex-wrap: wrap;
    }
    
    .feature {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 0.9rem;
    }
    
    .feature i {
      color: #28a745;
    }
    
    .simple-info-cards {
      padding: 2rem;
    }

    .info-cards-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
      margin-bottom: 2rem;
    }

    .info-card {
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 5px 20px rgba(0,0,0,0.1);
      transition: all 0.3s ease;
      animation: fadeInUp 0.6s ease-out forwards;
      opacity: 0;
      transform: translateY(20px);
    }

    .card-header {
      padding: 1.5rem;
      color: white;
      text-align: center;
      position: relative;
    }

    .card-icon {
      font-size: 2.5rem;
      margin-bottom: 0.5rem;
    }

    .card-header h3 {
      margin: 0 0 0.5rem 0;
      font-size: 1.3rem;
      font-weight: bold;
    }

    .card-highlight {
      background: rgba(255,255,255,0.2);
      padding: 0.25rem 0.75rem;
      border-radius: 20px;
      font-size: 0.8rem;
      font-weight: 500;
    }

    .card-content {
      padding: 1.5rem;
    }

    .card-details {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .card-details li {
      padding: 0.5rem 0;
      border-bottom: 1px solid #f0f0f0;
      position: relative;
      padding-left: 1.5rem;
    }

    .card-details li:before {
      content: '✓';
      position: absolute;
      left: 0;
      color: #28a745;
      font-weight: bold;
    }

    .card-details li:last-child {
      border-bottom: none;
    }

    .info-cards-footer {
      text-align: center;
      padding: 2rem;
      background: #f8f9fa;
      border-radius: 12px;
      margin-top: 2rem;
    }

    .download-brochure-btn {
      background: #0056b3;
      color: white;
      border: none;
      padding: 1rem 2rem;
      border-radius: 8px;
      font-size: 1.1rem;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      margin-top: 1rem;
    }

    .download-brochure-btn:hover {
      background: #004494;
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0,86,179,0.3);
    }

    .simple-advantages {
      padding: 2rem;
    }

    .advantages-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
    }
    
    .advantage-item {
      background: white;
      padding: 2rem;
      border-radius: 12px;
      text-align: center;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
      transition: all 0.3s ease;
      animation: fadeInUp 0.6s ease-out forwards;
      opacity: 0;
      transform: translateY(20px);
    }
    
    .advantage-icon {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 2rem;
      margin: 0 auto 1rem;
      color: white;
    }
    
    .advantage-item h4 {
      color: #0056b3;
      margin-bottom: 1rem;
      font-size: 1.3rem;
    }
    
    .advantage-item p {
      color: #666;
      line-height: 1.6;
    }
    
    .simple-modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    }
    
    .simple-modal .modal-content {
      background: white;
      border-radius: 8px;
      max-width: 90vw;
      max-height: 90vh;
      position: relative;
      overflow: hidden;
    }
    
    .simple-modal img {
      width: 100%;
      max-height: 60vh;
      object-fit: cover;
    }
    
    .simple-modal .modal-info {
      padding: 1.5rem;
    }
    
    .modal-close {
      position: absolute;
      top: 1rem;
      right: 1rem;
      background: rgba(0,0,0,0.5);
      color: white;
      border: none;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      font-size: 1.5rem;
      cursor: pointer;
      z-index: 1001;
    }
    
    @keyframes fadeInUp {
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
    
    @media (max-width: 768px) {
      .info-cards-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }

      .gallery-grid {
        grid-template-columns: 1fr;
      }

      .apply-features {
        flex-direction: column;
        gap: 1rem;
      }

      .simple-apply-button {
        padding: 2rem 1rem;
      }

      .advantages-grid {
        grid-template-columns: 1fr;
      }
    }
  `;
  
  document.head.appendChild(styles);
}

// Main initialization function (No modules required)
function initializeNoModuleComponents() {
  console.log('🚀 Initializing no-module components...');
  
  // Inject styles
  injectSimpleStylesNoModule();
  
  // Remove loading states
  function removeLoadingState(container) {
    const loadingElement = container.querySelector('.component-loading');
    if (loadingElement) {
      console.log('Removing loading state from:', container.id);
      loadingElement.style.opacity = '0';
      setTimeout(() => {
        if (loadingElement.parentNode) {
          loadingElement.remove();
        }
      }, 300);
    }
  }
  
  // Initialize components
  const components = [
    { id: 'info-cards-container', factory: createSimpleInfoCardsNoModule },
    { id: 'campus-gallery', factory: createSimpleCampusGalleryNoModule },
    { id: 'advantages-container', factory: createSimpleAdvantagesNoModule },
    { id: 'apply-button-container', factory: createSimpleApplyButtonNoModule }
  ];
  
  components.forEach(({ id, factory }) => {
    const container = document.getElementById(id);
    if (container) {
      try {
        console.log(`Initializing ${id}...`);
        removeLoadingState(container);
        
        const component = factory(container);
        if (component) {
          console.log(`✅ ${id} initialized successfully`);
        } else {
          console.warn(`⚠️ ${id} returned null`);
        }
      } catch (error) {
        console.error(`❌ ${id} failed:`, error);
        removeLoadingState(container);
        container.innerHTML = `
          <div style="text-align: center; padding: 2rem; color: #666;">
            <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 1rem; color: #ffc107;"></i>
            <h3>Component Unavailable</h3>
            <p>This component is temporarily unavailable.</p>
          </div>
        `;
      }
    } else {
      console.warn(`Container not found: ${id}`);
    }
  });
  
  // Force remove any remaining loading states after 3 seconds
  setTimeout(() => {
    const remainingLoading = document.querySelectorAll('.component-loading');
    if (remainingLoading.length > 0) {
      console.log('Force removing remaining loading states...');
      remainingLoading.forEach(el => {
        el.style.opacity = '0';
        setTimeout(() => el.remove(), 300);
      });
    }
  }, 3000);
  
  console.log('✅ No-module components initialization complete');
}

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeNoModuleComponents);
} else {
  initializeNoModuleComponents();
}
