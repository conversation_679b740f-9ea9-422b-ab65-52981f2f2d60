/* ===== PREMIUM MEDICAL EDUCATION ANIMATIONS ===== */
/* Sophisticated animations for high-end user experience */

/* ===== PREMIUM CARD ANIMATIONS ===== */
.premium-card {
  position: relative;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  transition: all var(--duration-500) var(--ease-premium);
  overflow: hidden;
}

.premium-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--duration-700) var(--ease-premium);
}

.premium-card:hover::before {
  left: 100%;
}

.premium-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-premium);
  border-color: var(--primary-300);
}

/* ===== FLOATING ELEMENTS ===== */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(1deg); }
  66% { transform: translateY(-5px) rotate(-1deg); }
}

.floating-element {
  animation: float 6s ease-in-out infinite;
}

/* ===== PREMIUM BUTTON ANIMATIONS ===== */
.premium-button {
  position: relative;
  background: var(--gradient-medical);
  border: none;
  border-radius: var(--radius-full);
  color: white;
  font-weight: var(--font-semibold);
  padding: var(--space-4) var(--space-8);
  cursor: pointer;
  overflow: hidden;
  transition: all var(--duration-300) var(--ease-premium);
  box-shadow: var(--shadow-lg);
}

.premium-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width var(--duration-500) var(--ease-elastic), height var(--duration-500) var(--ease-elastic);
}

.premium-button:hover::before {
  width: 300px;
  height: 300px;
}

.premium-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-premium);
}

.premium-button:active {
  transform: translateY(0);
}

/* ===== SCROLL REVEAL ANIMATIONS ===== */
.reveal-up {
  opacity: 0;
  transform: translateY(60px);
  transition: all var(--duration-700) var(--ease-premium);
}

.reveal-up.revealed {
  opacity: 1;
  transform: translateY(0);
}

.reveal-left {
  opacity: 0;
  transform: translateX(-60px);
  transition: all var(--duration-700) var(--ease-premium);
}

.reveal-left.revealed {
  opacity: 1;
  transform: translateX(0);
}

.reveal-right {
  opacity: 0;
  transform: translateX(60px);
  transition: all var(--duration-700) var(--ease-premium);
}

.reveal-right.revealed {
  opacity: 1;
  transform: translateX(0);
}

.reveal-scale {
  opacity: 0;
  transform: scale(0.8);
  transition: all var(--duration-700) var(--ease-bounce);
}

.reveal-scale.revealed {
  opacity: 1;
  transform: scale(1);
}

/* ===== PREMIUM LOADING ANIMATIONS ===== */
.premium-loader {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(135deg, var(--primary-50), var(--secondary-50));
}

.medical-pulse {
  width: 80px;
  height: 80px;
  border: 4px solid var(--primary-200);
  border-top: 4px solid var(--primary-600);
  border-radius: 50%;
  animation: medicalSpin 1s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite;
}

@keyframes medicalSpin {
  0% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
  100% { transform: rotate(360deg) scale(1); }
}

/* ===== HEARTBEAT ANIMATION ===== */
.heartbeat {
  animation: heartbeat 2s ease-in-out infinite;
}

@keyframes heartbeat {
  0%, 100% { transform: scale(1); }
  14% { transform: scale(1.1); }
  28% { transform: scale(1); }
  42% { transform: scale(1.1); }
  70% { transform: scale(1); }
}

/* ===== PREMIUM HOVER EFFECTS ===== */
.magnetic-hover {
  transition: transform var(--duration-300) var(--ease-premium);
}

.magnetic-hover:hover {
  transform: translateY(-4px);
}

.glow-on-hover {
  transition: all var(--duration-300) var(--ease-premium);
}

.glow-on-hover:hover {
  box-shadow: var(--shadow-glow);
  transform: translateY(-2px);
}

/* ===== STAGGERED ANIMATIONS ===== */
.stagger-item {
  opacity: 0;
  transform: translateY(30px);
  transition: all var(--duration-500) var(--ease-premium);
}

.stagger-item.animate {
  opacity: 1;
  transform: translateY(0);
}

.stagger-item:nth-child(1) { transition-delay: 0ms; }
.stagger-item:nth-child(2) { transition-delay: 100ms; }
.stagger-item:nth-child(3) { transition-delay: 200ms; }
.stagger-item:nth-child(4) { transition-delay: 300ms; }
.stagger-item:nth-child(5) { transition-delay: 400ms; }
.stagger-item:nth-child(6) { transition-delay: 500ms; }

/* ===== MORPHING SHAPES ===== */
.morphing-shape {
  border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  animation: morph 8s ease-in-out infinite;
}

@keyframes morph {
  0%, 100% { border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%; }
  25% { border-radius: 58% 42% 75% 25% / 76% 46% 54% 24%; }
  50% { border-radius: 50% 50% 33% 67% / 55% 27% 73% 45%; }
  75% { border-radius: 33% 67% 58% 42% / 63% 68% 32% 37%; }
}

/* ===== PREMIUM TEXT ANIMATIONS ===== */
.typewriter {
  overflow: hidden;
  border-right: 2px solid var(--primary-600);
  white-space: nowrap;
  animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink-caret {
  from, to { border-color: transparent; }
  50% { border-color: var(--primary-600); }
}

.gradient-text {
  background: var(--gradient-medical);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* ===== PARTICLE EFFECTS ===== */
.particle-container {
  position: relative;
  overflow: hidden;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--primary-400);
  border-radius: 50%;
  opacity: 0.7;
  animation: float-particle 6s linear infinite;
}

@keyframes float-particle {
  0% {
    transform: translateY(100vh) translateX(0);
    opacity: 0;
  }
  10% {
    opacity: 0.7;
  }
  90% {
    opacity: 0.7;
  }
  100% {
    transform: translateY(-100px) translateX(100px);
    opacity: 0;
  }
}

/* ===== RESPONSIVE PREMIUM ANIMATIONS ===== */
@media (max-width: 768px) {
  .premium-card:hover {
    transform: translateY(-4px) scale(1.01);
  }
  
  .reveal-up, .reveal-left, .reveal-right {
    transform: translateY(30px);
  }
  
  .reveal-up.revealed, .reveal-left.revealed, .reveal-right.revealed {
    transform: translateY(0);
  }
}

/* ===== PREMIUM FORM STYLING ===== */
.premium-input-wrapper {
  position: relative;
  margin-bottom: var(--space-6);
}

.premium-input-wrapper input,
.premium-input-wrapper textarea,
.premium-input-wrapper select {
  width: 100%;
  padding: var(--space-4) var(--space-6);
  border: 2px solid var(--color-border);
  border-radius: var(--radius-xl);
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  font-size: var(--text-base);
  transition: all var(--duration-300) var(--ease-premium);
  outline: none;
}

.premium-input-wrapper.focused input,
.premium-input-wrapper.focused textarea,
.premium-input-wrapper.focused select {
  border-color: var(--primary-500);
  box-shadow: var(--shadow-glow);
  transform: translateY(-2px);
}

.premium-input-wrapper.valid input,
.premium-input-wrapper.valid textarea,
.premium-input-wrapper.valid select {
  border-color: var(--success-500);
  box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
}

.premium-input-wrapper.invalid input,
.premium-input-wrapper.invalid textarea,
.premium-input-wrapper.invalid select {
  border-color: var(--error-500);
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* ===== RIPPLE EFFECT ===== */
.ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  transform: scale(0);
  animation: ripple-animation 0.6s linear;
  pointer-events: none;
}

@keyframes ripple-animation {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

/* ===== PREMIUM LOADING SCREEN ===== */
.premium-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-50), var(--secondary-50));
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  transition: opacity var(--duration-500) var(--ease-premium);
}

.loading-content {
  text-align: center;
  color: var(--color-text-primary);
}

.loading-content h3 {
  margin-bottom: var(--space-6);
  font-size: var(--text-2xl);
  font-weight: var(--font-semibold);
}

.progress-bar {
  width: 300px;
  height: 8px;
  background: var(--color-border);
  border-radius: var(--radius-full);
  overflow: hidden;
  margin: var(--space-4) 0;
}

.progress-fill {
  height: 100%;
  background: var(--gradient-medical);
  border-radius: var(--radius-full);
  transition: width var(--duration-300) var(--ease-premium);
  width: 0%;
}

.loading-percentage {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--primary-600);
}

@media (prefers-reduced-motion: reduce) {
  .premium-card, .premium-button, .reveal-up, .reveal-left, .reveal-right, .reveal-scale {
    transition: none;
    animation: none;
  }

  .floating-element, .heartbeat, .morphing-shape {
    animation: none;
  }
}
