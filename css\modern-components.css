/* ===== MODERN COMPONENT STYLES ===== */
/* Enhanced UI Components for Medical Education Platform */

/* ===== SECTION LAYOUT ===== */
.section-padding {
  padding: var(--space-24) var(--space-6);
  max-width: var(--container-7xl);
  margin: 0 auto;
  position: relative;
}

.section-title {
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  text-align: center;
  color: var(--color-text-primary);
  margin-bottom: var(--space-4);
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -var(--space-4);
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-500), var(--secondary-500));
  border-radius: var(--radius-full);
}

.section-description {
  text-align: center;
  font-size: var(--text-lg);
  color: var(--color-text-secondary);
  max-width: 600px;
  margin: 0 auto var(--space-16);
  line-height: var(--leading-relaxed);
}

/* ===== ENHANCED CAMPUS GALLERY ===== */
.simple-gallery {
  padding: var(--space-8) 0;
}

.gallery-header {
  text-align: center;
  margin-bottom: var(--space-12);
}

.gallery-header h3 {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--color-primary);
  margin-bottom: var(--space-4);
}

.gallery-header p {
  font-size: var(--text-lg);
  color: var(--color-text-secondary);
  max-width: 500px;
  margin: 0 auto;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--space-8);
  margin-bottom: var(--space-12);
}

.gallery-item {
  background: var(--color-surface);
  border-radius: var(--radius-2xl);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  transition: var(--transition-default);
  cursor: pointer;
  position: relative;
  group: gallery-item;
}

.gallery-item:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
}

.gallery-item img {
  width: 100%;
  height: 240px;
  object-fit: cover;
  transition: var(--transition-slow);
}

.gallery-item:hover img {
  transform: scale(1.05);
}

.gallery-item-info {
  padding: var(--space-6);
  position: relative;
}

.gallery-item-info h4 {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--color-primary);
  margin-bottom: var(--space-2);
}

.gallery-item-info p {
  color: var(--color-text-secondary);
  line-height: var(--leading-relaxed);
  margin-bottom: 0;
}

.gallery-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary-600), var(--secondary-600));
  opacity: 0;
  transition: var(--transition-default);
  z-index: 1;
}

.gallery-item:hover::before {
  opacity: 0.1;
}

/* ===== ENHANCED ADVANTAGES SECTION ===== */
.simple-advantages {
  padding: var(--space-8) 0;
}

.advantages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-8);
}

.advantage-item {
  background: var(--color-surface);
  padding: var(--space-8);
  border-radius: var(--radius-2xl);
  text-align: center;
  box-shadow: var(--shadow-lg);
  transition: var(--transition-default);
  position: relative;
  overflow: hidden;
  border: 1px solid var(--color-border);
}

.advantage-item:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-2xl);
  border-color: var(--primary-200);
}

.advantage-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-500), var(--secondary-500));
  transform: scaleX(0);
  transition: var(--transition-default);
}

.advantage-item:hover::before {
  transform: scaleX(1);
}

.advantage-icon {
  width: 100px;
  height: 100px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-4xl);
  margin: 0 auto var(--space-6);
  color: white;
  position: relative;
  box-shadow: var(--shadow-lg);
  transition: var(--transition-default);
}

.advantage-item:hover .advantage-icon {
  transform: scale(1.1);
  box-shadow: var(--shadow-xl);
}

.advantage-item h4 {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--space-4);
}

.advantage-item p {
  color: var(--color-text-secondary);
  line-height: var(--leading-relaxed);
  margin-bottom: 0;
}

/* ===== ENHANCED APPLY BUTTON ===== */
.simple-apply-button {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-800));
  border-radius: var(--radius-3xl);
  padding: var(--space-16) var(--space-8);
  text-align: center;
  color: white;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-2xl);
}

.simple-apply-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23dots)"/></svg>');
  opacity: 0.5;
}

.apply-content {
  position: relative;
  z-index: 2;
}

.apply-icon {
  font-size: var(--text-6xl);
  margin-bottom: var(--space-6);
  color: var(--secondary-400);
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.apply-content h3 {
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  margin-bottom: var(--space-4);
  color: white;
}

.apply-content p {
  font-size: var(--text-xl);
  margin-bottom: var(--space-8);
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  color: white;
}

.apply-btn {
  background: var(--secondary-500);
  color: var(--primary-900);
  border: none;
  padding: var(--space-5) var(--space-10);
  border-radius: var(--radius-full);
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  cursor: pointer;
  transition: var(--transition-default);
  display: inline-flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-8);
  box-shadow: var(--shadow-lg);
  min-height: 56px;
}

.apply-btn:hover {
  background: var(--secondary-400);
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.apply-btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.5), var(--shadow-xl);
}

.apply-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-6);
  margin-top: var(--space-8);
}

.feature {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  padding: var(--space-3) var(--space-4);
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-full);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature i {
  color: var(--success-400);
  font-size: var(--text-lg);
}

/* ===== ENHANCED MODAL ===== */
.simple-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(8px);
  }
}

.simple-modal .modal-content {
  background: var(--color-surface);
  border-radius: var(--radius-2xl);
  max-width: 90vw;
  max-height: 90vh;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-2xl);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.simple-modal img {
  width: 100%;
  max-height: 60vh;
  object-fit: cover;
}

.simple-modal .modal-info {
  padding: var(--space-8);
}

.simple-modal .modal-info h3 {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--color-primary);
  margin-bottom: var(--space-4);
}

.simple-modal .modal-info p {
  color: var(--color-text-secondary);
  line-height: var(--leading-relaxed);
  margin-bottom: 0;
}

.modal-close {
  position: absolute;
  top: var(--space-4);
  right: var(--space-4);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  width: 44px;
  height: 44px;
  border-radius: var(--radius-full);
  font-size: var(--text-xl);
  cursor: pointer;
  z-index: var(--z-popover);
  transition: var(--transition-default);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(8px);
}

.modal-close:hover,
.modal-close:focus {
  background: rgba(0, 0, 0, 0.9);
  transform: scale(1.1);
  outline: none;
}

/* ===== LOADING SPINNER ENHANCEMENT ===== */
.component-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  background: var(--color-surface);
  border-radius: var(--radius-2xl);
  border: 1px solid var(--color-border);
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid var(--primary-100);
  border-top: 4px solid var(--primary-600);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-4);
}

.component-loading::after {
  content: 'Loading...';
  font-size: var(--text-sm);
  color: var(--color-text-muted);
  font-weight: var(--font-medium);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .section-padding {
    padding: var(--space-20) var(--space-4);
  }
  
  .gallery-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-6);
  }
  
  .advantages-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-6);
  }
}

@media (max-width: 768px) {
  .section-title {
    font-size: var(--text-3xl);
  }
  
  .section-description {
    font-size: var(--text-base);
  }
  
  .gallery-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  .advantages-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  .apply-features {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }
  
  .simple-apply-button {
    padding: var(--space-12) var(--space-4);
  }
  
  .apply-content h3 {
    font-size: var(--text-3xl);
  }
  
  .apply-content p {
    font-size: var(--text-lg);
  }
  
  .apply-btn {
    font-size: var(--text-lg);
    padding: var(--space-4) var(--space-8);
  }
}

@media (max-width: 640px) {
  .section-padding {
    padding: var(--space-16) var(--space-2);
  }
  
  .advantage-item {
    padding: var(--space-6);
  }
  
  .advantage-icon {
    width: 80px;
    height: 80px;
    font-size: var(--text-3xl);
  }
  
  .simple-modal .modal-content {
    margin: var(--space-4);
    max-width: calc(100vw - 2rem);
  }
  
  .simple-modal .modal-info {
    padding: var(--space-6);
  }
}
